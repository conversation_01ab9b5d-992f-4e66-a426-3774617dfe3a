#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试bitmap字段处理的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from doris_stream_load import Doris<PERSON>tream<PERSON>oader

def test_bitmap_columns():
    """测试bitmap字段的columns参数生成"""
    
    # 创建一个模拟的表信息对象
    class MockTableInfo:
        def __init__(self):
            self.host = "localhost"
            self.port = 8030
            self.user = "root"
            self.password = ""
            self.database = "test_db"
            self.table = "test_table"
            self.columns = [
                'request_id', 'unique_request', 'request_count', 
                'app_name', 'stream_name'
            ]
            self.column_types = {
                'request_id': 'string',
                'unique_request': 'bitmap',  # bitmap类型
                'request_count': 'int',
                'app_name': 'string',
                'stream_name': 'string'
            }
    
    # 创建Stream Loader
    table_info = MockTableInfo()
    loader = DorisStreamLoader(table_info)
    
    print("=== 测试bitmap字段处理 ===\n")
    
    print("表结构:")
    for col in table_info.columns:
        col_type = table_info.column_types.get(col, 'unknown')
        print(f"  {col}: {col_type}")
    
    print("\n模拟columns参数生成逻辑:")
    
    # 模拟DorisStreamLoader中的逻辑
    has_bitmap_fields = False
    bitmap_columns = []
    
    for column in table_info.columns:
        column_lower = column.lower()
        column_type = table_info.column_types.get(column, 'string').lower()
        
        if 'bitmap' in column_type or 'unique_request' in column_lower:
            has_bitmap_fields = True
            bitmap_columns.append(column)
    
    print(f"检测到bitmap字段: {has_bitmap_fields}")
    print(f"bitmap字段列表: {bitmap_columns}")
    
    if has_bitmap_fields:
        column_mappings = []
        for column in table_info.columns:
            column_lower = column.lower()
            column_type = table_info.column_types.get(column, 'string').lower()
            
            if 'bitmap' in column_type or 'unique_request' in column_lower:
                column_mappings.append(f"{column}=to_bitmap({column})")
            else:
                column_mappings.append(column)
        
        columns_str = ",".join(column_mappings)
        print(f"\n生成的columns参数: {columns_str}")
    else:
        columns_str = ",".join(table_info.columns)
        print(f"\n标准columns参数: {columns_str}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_bitmap_columns()
