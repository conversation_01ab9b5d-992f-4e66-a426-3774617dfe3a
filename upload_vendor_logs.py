#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import gzip
import hashlib
import requests
import argparse
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 固定配置
API_PATH = "/openapi/v3/vendor/logs"
PRIVATE_KEY = "4GAqZKQLhQQ3CKUTvCBsUC6VCbJHK"  # 请替换为实际的私钥

def calculate_auth_key(uri, timestamp, rand, uid, private_key):
    """
    计算认证密钥

    参数:
    uri: API路径，例如 "/openapi/v3/vendor/logs"
    timestamp: 过期时间戳，unix timestamp格式
    rand: 随机数，这里固定为0
    uid: 用户ID，这里固定为0
    private_key: 私钥，由耕耘提供

    返回:
    auth_key: 格式为 '$timestamp-$rand-$uid-$token'
    """
    # 构造明文
    plain = f"{uri}-{timestamp}-{rand}-{uid}-{private_key}"

    # 计算MD5哈希
    md5hash = hashlib.md5(plain.encode('utf-8')).hexdigest()

    # 构造auth_key
    auth_key = f"{timestamp}-{rand}-{uid}-{md5hash}"

    return auth_key

def gzip_compress_file(input_file, output_file=None):
    """
    对文件进行gzip压缩

    参数:
    input_file: 输入文件路径
    output_file: 输出文件路径，如果为None则默认为input_file加上.gz后缀

    返回:
    compressed_file: 压缩后的文件路径
    """
    if output_file is None:
        output_file = input_file + '.gz'

    try:
        with open(input_file, 'rb') as f_in:
            with gzip.open(output_file, 'wb') as f_out:
                f_out.write(f_in.read())
        logger.info(f"文件压缩成功: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"文件压缩失败: {str(e)}")
        raise

def upload_vendor_logs(csv_file, host, gy_vendor, log_type, expire_minutes=5):
    """
    上传vendor_logs文件到指定API

    参数:
    csv_file: CSV文件路径
    host: API主机地址，例如 "api.example.com" 或 "https://api.example.com"
    gy_vendor: 供应商标识
    log_type: 日志类型
    expire_minutes: 认证过期时间（分钟）

    返回:
    response: 请求响应对象
    """
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"文件不存在: {csv_file}")

    # 压缩文件
    compressed_file = gzip_compress_file(csv_file)

    # 构造完整URL
    if not host.startswith(('http://', 'https://')):
        host = 'https://' + host

    # 添加查询参数


    # 计算认证参数
    timestamp = int(time.time() + expire_minutes * 60)  # 当前时间 + 过期时间
    rand = 0
    uid = 0

    # 计算认证密钥 - 注意：URI不包含查询参数
    auth_key = calculate_auth_key(API_PATH, timestamp, rand, uid, PRIVATE_KEY)
    logger.info(f"生成的auth_key: {auth_key}")

    api_url = f"{host.rstrip('/')}{API_PATH}?gy_vendor={gy_vendor}&log_type={log_type}&auth_key={auth_key}"
    logger.info(f"请求URL: {api_url}")

    # 准备请求头
    headers = {
        'Content-Type': 'application/octet-stream',
        'Content-Encoding': 'gzip'
    }

    # 读取压缩文件内容
    with open(compressed_file, 'rb') as f:
        compressed_data = f.read()

    # 发送请求
    try:
        response = requests.post(
            api_url,
            headers=headers,
            data=compressed_data
        )

        # 输出响应信息
        logger.info(f"请求状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")

        return response
    except Exception as e:
        logger.error(f"请求失败: {str(e)}")
        raise
    finally:
        # 清理临时文件
        if os.path.exists(compressed_file):
            os.remove(compressed_file)
            logger.info(f"临时文件已删除: {compressed_file}")

def main():
    parser = argparse.ArgumentParser(description='上传vendor_logs到API')
    parser.add_argument('--file', type=str, default='vendor_logs.csv', help='CSV文件路径')
    parser.add_argument('--host', type=str, required=True, help='API主机地址，例如 "api.example.com"')
    parser.add_argument('--gy_vendor', type=str, required=True, help='供应商标识')
    parser.add_argument('--log_type', type=str, required=True, help='日志类型')
    parser.add_argument('--expire', type=int, default=5, help='认证过期时间（分钟）')

    args = parser.parse_args()

    try:
        response = upload_vendor_logs(
            args.file,
            args.host,
            args.gy_vendor,
            args.log_type,
            args.expire
        )

        if response.status_code == 200:
            logger.info("上传成功!")
        else:
            logger.error(f"上传失败! 状态码: {response.status_code}")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()
