# Doris S3 导出性能测试工具

针对您提供的 SQL 查询的性能测试工具，可以测试 1、5、10 个并发查询的执行耗时和性能表现。

## 测试的 SQL 查询

```sql
SELECT *
FROM tb_monitor_realtime_general_cdn_request_info_v2
WHERE window_start BETWEEN '2025-05-14 00:00:00' AND '2025-05-14 02:59:59'
INTO OUTFILE "s3://test/test_"
FORMAT AS parquet
PROPERTIES(
    "s3.endpoint" = "http://172.16.106.93:9000",
    "s3.region" = "cn_beijing",
    "s3.access_key" = "root",
    "s3.secret_key" = "As110695",
    "use_path_style" = "true",
    "max_file_size" = "8MB"
);
```

## 工具特性

- 🚀 **并发测试**: 支持 1、5、10 个并发查询测试
- 📊 **详细统计**: 提供平均、最小、最大、中位数耗时统计
- 🔄 **自动文件命名**: 避免并发查询的文件名冲突
- 📈 **性能分析**: 计算加速比、效率和吞吐量
- 🛡️ **错误处理**: 完善的异常处理和错误报告

## 安装依赖

```bash
pip install pymysql
```

## 使用方法

### 方法1: 使用简化版本（推荐）

```bash
python s3_export_performance_test.py
```

运行前需要修改脚本中的数据库连接配置：

```python
config = {
    'host': '你的Doris主机IP',
    'port': 9030,
    'user': 'root',
    'password': '你的密码',
    'database': '你的数据库名'
}
```

### 方法2: 使用完整版本

```bash
python doris_performance_test.py \
    --host ************* \
    --port 9030 \
    --user root \
    --password your_password \
    --database your_database \
    --start-time "2025-05-14 00:00:00" \
    --end-time "2025-05-14 02:59:59" \
    --concurrency "1,5,10"
```

## 测试输出示例

```
Doris S3 导出性能测试
测试查询: tb_monitor_realtime_general_cdn_request_info_v2
时间范围: 2025-05-14 00:00:00 到 2025-05-14 02:59:59
导出格式: Parquet
目标: S3 存储

============================================================
开始 1 个并发 S3 导出测试
============================================================

[Query-1] 开始执行 S3 导出...
[Query-1] 完成，耗时: 45.23秒

测试结果:
总耗时: 45.23秒
成功查询: 1
失败查询: 0
平均查询耗时: 45.23秒
最快查询耗时: 45.23秒
最慢查询耗时: 45.23秒
中位数耗时: 45.23秒
吞吐量: 0.02 查询/秒

============================================================
开始 5 个并发 S3 导出测试
============================================================

[Query-1] 开始执行 S3 导出...
[Query-2] 开始执行 S3 导出...
[Query-3] 开始执行 S3 导出...
[Query-4] 开始执行 S3 导出...
[Query-5] 开始执行 S3 导出...
[Query-2] 完成，耗时: 52.15秒
[Query-1] 完成，耗时: 53.42秒
[Query-3] 完成，耗时: 54.18秒
[Query-4] 完成，耗时: 55.23秒
[Query-5] 完成，耗时: 56.12秒

测试结果:
总耗时: 56.12秒
成功查询: 5
失败查询: 0
平均查询耗时: 54.22秒
最快查询耗时: 52.15秒
最慢查询耗时: 56.12秒
中位数耗时: 54.18秒
吞吐量: 0.09 查询/秒

================================================================================
性能测试汇总报告
================================================================================
并发数   总耗时(s)    成功数   失败数   平均耗时(s)  吞吐量(q/s)
--------------------------------------------------------------------------------
1        45.23        1        0        45.23        0.02
5        56.12        5        0        54.22        0.09
10       78.45        10       0        72.34        0.13

性能分析:
  5并发相比1并发:
    理论加速比: 5x
    实际加速比: 4.03x
    并发效率: 80.6%
    吞吐量提升: 4.50x
  10并发相比1并发:
    理论加速比: 10x
    实际加速比: 5.76x
    并发效率: 57.6%
    吞吐量提升: 6.50x
```

## 测试指标说明

### 基本指标
- **总耗时**: 所有并发查询完成的总时间
- **成功数/失败数**: 成功和失败的查询数量
- **平均耗时**: 单个查询的平均执行时间
- **吞吐量**: 每秒完成的查询数量

### 性能分析指标
- **理论加速比**: 理想情况下的性能提升倍数
- **实际加速比**: 实际测得的性能提升倍数
- **并发效率**: 实际加速比与理论加速比的比值
- **吞吐量提升**: 相对于单并发的吞吐量提升倍数

## 注意事项

1. **S3 存储**: 确保 S3 存储服务正常运行且有足够空间
2. **网络带宽**: S3 导出会消耗大量网络带宽
3. **文件冲突**: 工具自动生成唯一文件名避免冲突
4. **超时设置**: 默认设置 10 分钟超时，可根据数据量调整
5. **资源监控**: 建议监控 Doris 集群的 CPU、内存、网络使用情况

## 性能优化建议

1. **并发数调优**: 根据测试结果选择最优并发数
2. **文件大小**: 调整 `max_file_size` 参数优化性能
3. **网络优化**: 确保 Doris 到 S3 的网络连接稳定
4. **资源配置**: 根据测试结果调整 Doris 集群资源配置

## 故障排除

### 常见错误
- **连接超时**: 检查网络连接和防火墙设置
- **认证失败**: 检查 S3 访问密钥和权限
- **磁盘空间不足**: 检查 S3 存储空间
- **内存不足**: 调整查询的数据量或增加内存

### 调试方法
- 查看 Doris FE/BE 日志
- 检查 S3 服务状态
- 监控系统资源使用情况
- 使用单并发测试排除问题
