# Doris Stream Load 数据生成工具

一个用于生成符合 Apache Doris 表结构的 CSV 数据并通过 Stream Load 导入的 Python 工具。

## 功能特性

- 🚀 **自动表结构获取**: 自动从 Doris 数据库获取表结构信息
- 📊 **智能数据生成**: 根据字段类型和名称生成符合现实场景的测试数据
- 🔄 **批量导入支持**: 支持分批次生成和导入大量数据
- 🎯 **特殊字段优化**: 针对特定业务字段提供优化的数据生成逻辑
- 📈 **Bitmap 字段支持**: 支持 Doris bitmap 类型字段的正确处理
- 🛠️ **灵活配置**: 支持自定义列结构和数据类型

## 安装依赖

```bash
pip install pymysql requests faker
```

## 快速开始

### 基本用法

```bash
# 生成1000行数据并导入到Doris
python doris_stream_load.py \
    --host 127.0.0.1 \
    --database test_db \
    --table test_table \
    --rows 1000
```

### 仅生成CSV文件（不导入）

```bash
python doris_stream_load.py \
    --host 127.0.0.1 \
    --database test_db \
    --table test_table \
    --rows 1000 \
    --no-import
```

### 分批次导入

```bash
# 分10个批次，每批次1000行
python doris_stream_load.py \
    --host 127.0.0.1 \
    --database test_db \
    --table test_table \
    --batch-size 1000 \
    --total-batches 10
```

## 命令行参数

### Doris 连接参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--host` | string | 必填 | Doris FE 主机地址 |
| `--port` | int | 8030 | Doris HTTP 端口 |
| `--mysql-port` | int | 9030 | Doris MySQL 端口 |
| `--user` | string | root | Doris 用户名 |
| `--password` | string | "" | Doris 密码 |
| `--database` | string | 必填 | 数据库名 |
| `--table` | string | 必填 | 表名 |

### CSV 生成参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--output` | string | doris_data.csv | 输出文件路径 |
| `--rows` | int | 1000 | 生成的数据行数 |
| `--delimiter` | string | , | CSV 分隔符 |

### 导入参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--import` | flag | true | 生成后立即导入 |
| `--no-import` | flag | false | 仅生成不导入 |
| `--max-filter-ratio` | float | 0.1 | 最大过滤比例 |

### 批次参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--batch-size` | int | 0 | 每批次行数（0表示不分批） |
| `--batch-number` | int | 0 | 指定批次编号（0表示所有批次） |
| `--total-batches` | int | 1 | 总批次数 |

## 特殊字段支持

工具针对以下字段提供了优化的数据生成逻辑：

### 业务字段
- `request_id`: 生成具有一定重复性的请求ID
- `request_uri`: 生成符合直觉的URI路径
- `request_count`: 始终为1
- `unique_request`: 使用bitmap_hash64函数处理的bitmap字段

### 应用字段
- `app_name`: 25种真实应用名称
- `stream_name`: 25种真实流名称
- `stream_protocol`: 10种流媒体协议

### 网络字段
- `client_ip`: 80%内网IP，20%公网IP
- `service_ip`: 主要生成内网IP
- `domain`: 真实的域名格式

### 地理字段
- `province`: 中国省份名称
- `country`: 国家名称
- `region`: cn/eu/an区域代码

### 其他字段
- `tenant_id`: 1000001-1000050范围
- `http_code`: 按真实分布的HTTP状态码
- `isp`: 运营商名称
- `cdn_vendor`: CDN厂商名称

## 使用示例

### 示例1：基本数据生成

```bash
python doris_stream_load.py \
    --host ************* \
    --database analytics \
    --table user_events \
    --rows 5000 \
    --user admin \
    --password mypassword
```

### 示例2：大批量数据导入

```bash
# 生成100万行数据，分100个批次
python doris_stream_load.py \
    --host ************* \
    --database analytics \
    --table user_events \
    --batch-size 10000 \
    --total-batches 100 \
    --max-filter-ratio 0.2
```

### 示例3：自定义列结构

```bash
python doris_stream_load.py \
    --host ************* \
    --database test \
    --table custom_table \
    --columns "id,name,age,email" \
    --types "int,string,int,string" \
    --rows 1000
```

## 注意事项

1. **Bitmap 字段**: 对于 bitmap 类型的字段（如 unique_request），工具会自动使用 `bitmap_hash64` 函数进行转换
2. **数据一致性**: `unique_request` 字段会使用与 `request_id` 相同的值
3. **网络连接**: 确保能够连接到 Doris 的 HTTP 端口（默认8030）和 MySQL 端口（默认9030）
4. **权限要求**: 用户需要有目标表的 INSERT 权限

## 故障排除

### 常见错误

1. **连接失败**: 检查主机地址、端口和网络连通性
2. **认证失败**: 检查用户名和密码
3. **表不存在**: 检查数据库名和表名是否正确
4. **类型不匹配**: 检查生成的数据类型是否与表结构匹配

### 调试模式

```bash
python doris_stream_load.py \
    --host 127.0.0.1 \
    --database test_db \
    --table test_table \
    --debug
```

## 数据生成规则

### 数据类型映射

| Doris 类型 | 生成规则 | 示例值 |
|------------|----------|--------|
| `string/varchar` | 随机单词 | "data", "test", "sample" |
| `int/integer` | 1-10000随机整数 | 1234 |
| `bigint` | 1-1000000随机整数 | 567890 |
| `float/double` | 0.1-1000.0随机浮点数 | 123.456 |
| `boolean` | 随机布尔值 | true/false |
| `date` | 主要使用今天和昨天 | "2024-01-15" |
| `datetime` | 昨天的随机时间 | "2024-01-14 15:30:00" |

### 特殊字段生成逻辑

#### Request ID 生成
- 70% 概率从预定义池中选择（保证重复性）
- 30% 概率生成新ID
- 支持4种格式：UUID、时间戳、纯数字、字母数字混合

#### IP 地址生成
- **客户端IP**: 80%内网IP，20%公网IP
- **服务端IP**: 主要生成内网IP段
- 支持常见的内网段：192.168.x.x, 10.x.x.x, 172.16-31.x.x

#### URI 生成
- 30种预定义路径：`/api/v1/stream`, `/live/channel` 等
- 30% 概率添加查询参数
- 支持流媒体相关的路径和参数

## 性能优化

### 批量处理建议

```bash
# 推荐：大数据量分批处理
python doris_stream_load.py \
    --host 127.0.0.1 \
    --database test_db \
    --table test_table \
    --batch-size 50000 \
    --total-batches 20 \
    --max-filter-ratio 0.1
```

### 内存使用优化

- 单批次建议不超过10万行
- 大表建议使用分批导入
- 可通过 `--max-filter-ratio` 调整容错率

## 配置文件支持

可以创建配置文件来简化命令行参数：

```json
{
    "host": "*************",
    "port": 8030,
    "user": "admin",
    "password": "password",
    "database": "analytics",
    "table": "events",
    "batch_size": 10000,
    "max_filter_ratio": 0.1
}
```

## API 使用

除了命令行工具，也可以作为 Python 模块使用：

```python
from doris_stream_load import DorisTableInfo, CSVGenerator, DorisStreamLoader

# 创建表信息对象
table_info = DorisTableInfo(
    host="127.0.0.1",
    port=8030,
    user="root",
    password="",
    database="test_db",
    table="test_table"
)

# 生成CSV数据
generator = CSVGenerator(table_info)
csv_file = generator.generate_csv("output.csv", 1000)

# 导入到Doris
loader = DorisStreamLoader(table_info)
success, result = loader.load_csv(csv_file)
```