#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Doris S3 导出性能测试
专门测试 S3 导出查询的并发性能
"""

import time
import pymysql
import threading
import uuid
from concurrent.futures import ThreadPoolExecutor
import statistics

class S3ExportPerformanceTest:
    def __init__(self, host, port, user, password, database):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(
            host=self.host,
            port=self.port,
            user=self.user,
            password=self.password,
            database=self.database,
            connect_timeout=30,
            read_timeout=600,  # 10分钟超时
            write_timeout=600
        )
    
    def execute_s3_export(self, query_id):
        """执行单个S3导出查询"""
        # 生成唯一文件名避免冲突
        unique_suffix = f"{query_id}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        sql = f"""
        SELECT *
        FROM tb_monitor_realtime_general_cdn_request_info_v2
        WHERE window_start BETWEEN '2025-05-14 00:00:00' AND '2025-05-14 02:59:59'
        INTO OUTFILE "s3://test/test_{unique_suffix}"
        FORMAT AS parquet
        PROPERTIES(
            "s3.endpoint" = "http://*************:9000",
            "s3.region" = "cn_beijing",
            "s3.access_key" = "root",
            "s3.secret_key" = "As110695",
            "use_path_style" = "true",
            "max_file_size" = "8MB"
        )
        """
        
        start_time = time.time()
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            print(f"[{query_id}] 开始执行 S3 导出...")
            cursor.execute(sql)
            
            # 获取执行结果
            result = cursor.fetchall()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"[{query_id}] 完成，耗时: {duration:.2f}秒")
            
            cursor.close()
            conn.close()
            
            return {
                'query_id': query_id,
                'duration': duration,
                'success': True,
                'error': None,
                'file_name': f"test_{unique_suffix}"
            }
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            error_msg = str(e)
            
            print(f"[{query_id}] 失败: {error_msg}")
            
            return {
                'query_id': query_id,
                'duration': duration,
                'success': False,
                'error': error_msg,
                'file_name': f"test_{unique_suffix}"
            }
    
    def run_concurrent_test(self, concurrency):
        """运行指定并发数的测试"""
        print(f"\n{'='*60}")
        print(f"开始 {concurrency} 个并发 S3 导出测试")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        # 使用线程池执行并发查询
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = []
            
            # 提交所有任务
            for i in range(concurrency):
                future = executor.submit(self.execute_s3_export, f"Query-{i+1}")
                futures.append(future)
            
            # 收集结果
            results = []
            for future in futures:
                result = future.result()
                results.append(result)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 分析结果
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        print(f"\n测试结果:")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"成功查询: {len(successful)}")
        print(f"失败查询: {len(failed)}")
        
        if successful:
            durations = [r['duration'] for r in successful]
            avg_duration = statistics.mean(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            median_duration = statistics.median(durations)
            
            print(f"平均查询耗时: {avg_duration:.2f}秒")
            print(f"最快查询耗时: {min_duration:.2f}秒")
            print(f"最慢查询耗时: {max_duration:.2f}秒")
            print(f"中位数耗时: {median_duration:.2f}秒")
            
            # 计算吞吐量
            throughput = len(successful) / total_duration
            print(f"吞吐量: {throughput:.2f} 查询/秒")
        
        if failed:
            print(f"\n失败的查询:")
            for fail in failed:
                print(f"  {fail['query_id']}: {fail['error']}")
        
        return {
            'concurrency': concurrency,
            'total_duration': total_duration,
            'successful_count': len(successful),
            'failed_count': len(failed),
            'avg_duration': avg_duration if successful else 0,
            'min_duration': min_duration if successful else 0,
            'max_duration': max_duration if successful else 0,
            'median_duration': median_duration if successful else 0,
            'throughput': len(successful) / total_duration if successful else 0
        }
    
    def run_all_tests(self):
        """运行 1、5、10 并发测试"""
        concurrency_levels = [1, 5, 10]
        all_results = []
        
        print("Doris S3 导出性能测试")
        print("测试查询: tb_monitor_realtime_general_cdn_request_info_v2")
        print("时间范围: 2025-05-14 00:00:00 到 2025-05-14 02:59:59")
        print("导出格式: Parquet")
        print("目标: S3 存储")
        
        for concurrency in concurrency_levels:
            result = self.run_concurrent_test(concurrency)
            all_results.append(result)
            
            # 测试间隔
            if concurrency != concurrency_levels[-1]:
                print(f"\n等待 10 秒后进行下一组测试...")
                time.sleep(10)
        
        # 打印汇总报告
        self.print_summary_report(all_results)
        
        return all_results
    
    def print_summary_report(self, results):
        """打印汇总报告"""
        print(f"\n{'='*80}")
        print("性能测试汇总报告")
        print(f"{'='*80}")
        
        # 表头
        print(f"{'并发数':<8} {'总耗时(s)':<12} {'成功数':<8} {'失败数':<8} {'平均耗时(s)':<12} {'吞吐量(q/s)':<12}")
        print("-" * 80)
        
        # 数据行
        for result in results:
            print(f"{result['concurrency']:<8} "
                  f"{result['total_duration']:<12.2f} "
                  f"{result['successful_count']:<8} "
                  f"{result['failed_count']:<8} "
                  f"{result['avg_duration']:<12.2f} "
                  f"{result['throughput']:<12.2f}")
        
        # 性能分析
        if len(results) > 1:
            print(f"\n性能分析:")
            baseline = results[0]  # 1并发作为基准
            
            for result in results[1:]:
                if baseline['avg_duration'] > 0 and result['avg_duration'] > 0:
                    # 计算相对性能
                    speedup = baseline['total_duration'] / result['total_duration'] * result['concurrency']
                    efficiency = speedup / result['concurrency'] * 100
                    
                    print(f"  {result['concurrency']}并发相比1并发:")
                    print(f"    理论加速比: {result['concurrency']}x")
                    print(f"    实际加速比: {speedup:.2f}x")
                    print(f"    并发效率: {efficiency:.1f}%")
                    print(f"    吞吐量提升: {result['throughput']/baseline['throughput']:.2f}x")

def main():
    # 数据库连接配置
    config = {
        'host': '127.0.0.1',      # 修改为您的 Doris 主机
        'port': 9030,             # Doris MySQL 端口
        'user': 'root',           # 用户名
        'password': '',           # 密码
        'database': 'your_db'     # 数据库名
    }
    
    print("请确认数据库连接配置:")
    print(f"主机: {config['host']}")
    print(f"端口: {config['port']}")
    print(f"用户: {config['user']}")
    print(f"数据库: {config['database']}")
    
    confirm = input("\n配置正确吗? (y/n): ")
    if confirm.lower() != 'y':
        print("请修改脚本中的配置信息后重新运行")
        return
    
    try:
        # 创建测试实例
        tester = S3ExportPerformanceTest(**config)
        
        # 运行测试
        results = tester.run_all_tests()
        
        print(f"\n测试完成! 共执行了 {sum(r['successful_count'] + r['failed_count'] for r in results)} 个查询")
        
    except Exception as e:
        print(f"测试执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
