#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Doris SQL 性能测试工具
测试 S3 导出查询在不同并发数下的性能表现
"""

import time
import threading
import pymysql
import argparse
import logging
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DorisPerformanceTest:
    """Doris 性能测试类"""
    
    def __init__(self, host, port, user, password, database):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.results = []
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=30,
                read_timeout=300,  # 5分钟读取超时
                write_timeout=300  # 5分钟写入超时
            )
            return conn
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            raise
    
    def generate_test_sql(self, start_time, end_time, file_suffix=""):
        """生成测试SQL"""
        sql = f"""
        SELECT *
        FROM tb_monitor_realtime_general_cdn_request_info_v2
        WHERE window_start BETWEEN '{start_time}' AND '{end_time}'
        INTO OUTFILE "s3://test/test_{file_suffix}"
        FORMAT AS parquet
        PROPERTIES(
            "s3.endpoint" = "http://*************:9000",
            "s3.region" = "cn_beijing",
            "s3.access_key" = "root",
            "s3.secret_key" = "As110695",
            "use_path_style" = "true",
            "max_file_size" = "8MB"
        )
        """
        return sql.strip()
    
    def execute_single_query(self, query_id, start_time, end_time):
        """执行单个查询"""
        conn = None
        try:
            # 生成唯一的文件后缀
            file_suffix = f"{query_id}_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
            sql = self.generate_test_sql(start_time, end_time, file_suffix)
            
            logger.info(f"查询 {query_id} 开始执行...")
            start = time.time()
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 执行查询
            cursor.execute(sql)
            
            # 获取结果（如果有的话）
            result = cursor.fetchall()
            
            end = time.time()
            duration = end - start
            
            logger.info(f"查询 {query_id} 完成，耗时: {duration:.2f}秒")
            
            return {
                'query_id': query_id,
                'duration': duration,
                'success': True,
                'error': None,
                'file_suffix': file_suffix,
                'result_count': len(result) if result else 0
            }
            
        except Exception as e:
            end = time.time()
            duration = end - start if 'start' in locals() else 0
            error_msg = str(e)
            
            logger.error(f"查询 {query_id} 失败: {error_msg}")
            
            return {
                'query_id': query_id,
                'duration': duration,
                'success': False,
                'error': error_msg,
                'file_suffix': file_suffix if 'file_suffix' in locals() else '',
                'result_count': 0
            }
        finally:
            if conn:
                conn.close()
    
    def run_concurrent_test(self, concurrency, start_time, end_time):
        """运行并发测试"""
        logger.info(f"开始 {concurrency} 个并发查询测试...")
        
        test_start = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            # 提交所有任务
            futures = []
            for i in range(concurrency):
                future = executor.submit(
                    self.execute_single_query, 
                    f"query_{i+1}", 
                    start_time, 
                    end_time
                )
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        test_end = time.time()
        total_duration = test_end - test_start
        
        # 统计结果
        successful_queries = [r for r in results if r['success']]
        failed_queries = [r for r in results if not r['success']]
        
        if successful_queries:
            durations = [r['duration'] for r in successful_queries]
            avg_duration = statistics.mean(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            median_duration = statistics.median(durations)
        else:
            avg_duration = min_duration = max_duration = median_duration = 0
        
        test_result = {
            'concurrency': concurrency,
            'total_duration': total_duration,
            'successful_count': len(successful_queries),
            'failed_count': len(failed_queries),
            'avg_duration': avg_duration,
            'min_duration': min_duration,
            'max_duration': max_duration,
            'median_duration': median_duration,
            'results': results
        }
        
        self.results.append(test_result)
        
        logger.info(f"{concurrency} 个并发测试完成:")
        logger.info(f"  总耗时: {total_duration:.2f}秒")
        logger.info(f"  成功查询: {len(successful_queries)}")
        logger.info(f"  失败查询: {len(failed_queries)}")
        if successful_queries:
            logger.info(f"  平均耗时: {avg_duration:.2f}秒")
            logger.info(f"  最短耗时: {min_duration:.2f}秒")
            logger.info(f"  最长耗时: {max_duration:.2f}秒")
            logger.info(f"  中位数耗时: {median_duration:.2f}秒")
        
        return test_result
    
    def run_all_tests(self, start_time, end_time, concurrency_levels=[1, 5, 10]):
        """运行所有并发级别的测试"""
        logger.info("开始性能测试...")
        logger.info(f"测试时间范围: {start_time} 到 {end_time}")
        logger.info(f"测试并发级别: {concurrency_levels}")
        
        for concurrency in concurrency_levels:
            logger.info(f"\n{'='*50}")
            self.run_concurrent_test(concurrency, start_time, end_time)
            
            # 在不同并发测试之间稍作休息
            if concurrency != concurrency_levels[-1]:
                logger.info("等待5秒后进行下一组测试...")
                time.sleep(5)
        
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        logger.info(f"\n{'='*60}")
        logger.info("性能测试总结")
        logger.info(f"{'='*60}")
        
        print(f"{'并发数':<8} {'总耗时(s)':<12} {'成功数':<8} {'失败数':<8} {'平均耗时(s)':<12} {'最短耗时(s)':<12} {'最长耗时(s)':<12}")
        print("-" * 80)
        
        for result in self.results:
            print(f"{result['concurrency']:<8} "
                  f"{result['total_duration']:<12.2f} "
                  f"{result['successful_count']:<8} "
                  f"{result['failed_count']:<8} "
                  f"{result['avg_duration']:<12.2f} "
                  f"{result['min_duration']:<12.2f} "
                  f"{result['max_duration']:<12.2f}")
        
        # 分析性能趋势
        if len(self.results) > 1:
            logger.info(f"\n性能分析:")
            baseline = self.results[0]  # 单并发作为基准
            for result in self.results[1:]:
                if baseline['avg_duration'] > 0:
                    speedup = baseline['avg_duration'] / result['avg_duration']
                    efficiency = speedup / result['concurrency'] * 100
                    logger.info(f"  {result['concurrency']}并发 vs 1并发: "
                              f"加速比 {speedup:.2f}x, "
                              f"效率 {efficiency:.1f}%")

def main():
    parser = argparse.ArgumentParser(description='Doris SQL 性能测试工具')
    
    # 数据库连接参数
    parser.add_argument('--host', type=str, required=True, help='Doris 主机地址')
    parser.add_argument('--port', type=int, default=9030, help='Doris MySQL 端口，默认9030')
    parser.add_argument('--user', type=str, default='root', help='用户名，默认root')
    parser.add_argument('--password', type=str, default='', help='密码')
    parser.add_argument('--database', type=str, required=True, help='数据库名')
    
    # 测试参数
    parser.add_argument('--start-time', type=str, default='2025-05-14 00:00:00', 
                       help='查询开始时间，默认2025-05-14 00:00:00')
    parser.add_argument('--end-time', type=str, default='2025-05-14 02:59:59',
                       help='查询结束时间，默认2025-05-14 00:59:59')
    parser.add_argument('--concurrency', type=str, default='1,5,10',
                       help='并发级别，逗号分隔，默认1,5,10')
    
    args = parser.parse_args()
    
    # 解析并发级别
    concurrency_levels = [int(x.strip()) for x in args.concurrency.split(',')]
    
    try:
        # 创建测试实例
        tester = DorisPerformanceTest(
            host=args.host,
            port=args.port,
            user=args.user,
            password=args.password,
            database=args.database
        )
        
        # 运行测试
        tester.run_all_tests(
            start_time=args.start_time,
            end_time=args.end_time,
            concurrency_levels=concurrency_levels
        )
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
