#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试unique_request字段处理的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from doris_stream_load import CSVGenerator

def test_unique_request_generation():
    """测试unique_request字段的生成功能"""
    
    # 创建一个模拟的表信息对象
    class MockTableInfo:
        def __init__(self):
            self.columns = [
                'request_id', 'unique_request', 'request_count', 
                'app_name', 'stream_name'
            ]
            self.column_types = {
                'request_id': 'string',
                'unique_request': 'bitmap',  # bitmap类型
                'request_count': 'int',
                'app_name': 'string',
                'stream_name': 'string'
            }
    
    # 创建CSV生成器
    table_info = MockTableInfo()
    generator = CSVGenerator(table_info)
    
    print("=== 测试unique_request字段生成 ===\n")
    
    print("表结构:")
    for col in table_info.columns:
        col_type = table_info.column_types.get(col, 'unknown')
        print(f"  {col}: {col_type}")
    
    print("\n测试数据生成:")
    
    # 生成几行数据来验证unique_request等于request_id
    for i in range(5):
        row = generator.generate_row()
        print(f"  行 {i+1}: {', '.join(row)}")
        
        # 找到request_id和unique_request的位置
        request_id_idx = None
        unique_request_idx = None
        
        for j, col in enumerate(table_info.columns):
            if 'request_id' in col.lower():
                request_id_idx = j
            elif 'unique_request' in col.lower():
                unique_request_idx = j
        
        if request_id_idx is not None and unique_request_idx is not None:
            request_id_value = row[request_id_idx]
            unique_request_value = row[unique_request_idx]
            
            if request_id_value == unique_request_value:
                print(f"    ✓ unique_request ({unique_request_value}) 等于 request_id ({request_id_value})")
            else:
                print(f"    ✗ unique_request ({unique_request_value}) 不等于 request_id ({request_id_value})")
    
    print("\n=== 测试完成 ===")

def test_columns_mapping():
    """测试columns参数的映射逻辑"""
    
    # 创建一个模拟的表信息对象
    class MockTableInfo:
        def __init__(self):
            self.host = "localhost"
            self.port = 8030
            self.user = "root"
            self.password = ""
            self.database = "test_db"
            self.table = "test_table"
            self.columns = [
                'request_id', 'unique_request', 'app_name'
            ]
            self.column_types = {
                'request_id': 'string',
                'unique_request': 'bitmap',
                'app_name': 'string'
            }
    
    table_info = MockTableInfo()
    
    print("\n=== 测试columns映射逻辑 ===\n")
    
    # 模拟DorisStreamLoader中的逻辑
    has_unique_request = any('unique_request' in col.lower() for col in table_info.columns)
    print(f"检测到unique_request字段: {has_unique_request}")
    
    if has_unique_request:
        column_mappings = []
        for column in table_info.columns:
            column_lower = column.lower()
            if 'unique_request' in column_lower:
                column_mappings.append(f"{column}=bitmap_hash({column})")
            else:
                column_mappings.append(column)
        
        columns_str = ",".join(column_mappings)
        print(f"生成的columns参数: {columns_str}")
        
        # 验证映射是否正确
        expected = "request_id,unique_request=bitmap_hash(unique_request),app_name"
        if columns_str == expected:
            print("✓ columns映射正确")
        else:
            print(f"✗ columns映射错误，期望: {expected}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_unique_request_generation()
    test_columns_mapping()
