#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试columns参数格式的脚本
"""

def test_columns_format():
    """测试新的columns参数格式"""
    
    # 模拟表结构
    columns = ['request_id', 'unique_request', 'app_name', 'stream_name']
    
    print("=== 测试columns参数格式 ===\n")
    print(f"原始列名: {columns}")
    
    # 检查是否有unique_request字段
    has_unique_request = any('unique_request' in col.lower() for col in columns)
    print(f"检测到unique_request字段: {has_unique_request}")
    
    if has_unique_request:
        # 构建columns参数，使用临时列的方式处理unique_request字段
        temp_columns = []
        final_mappings = []
        
        for column in columns:
            column_lower = column.lower()
            if 'unique_request' in column_lower:
                # 对unique_request字段，使用临时列名
                temp_col_name = f"temp_{column}"
                temp_columns.append(temp_col_name)
                final_mappings.append(f"{column}=bitmap_hash({temp_col_name})")
            else:
                # 普通字段直接使用列名
                temp_columns.append(column)

        # 组合临时列名和最终映射
        columns_str = ",".join(temp_columns + final_mappings)
        print(f"\n生成的columns参数: {columns_str}")
        
        # 分解显示
        print(f"\n临时列部分: {','.join(temp_columns)}")
        print(f"映射部分: {','.join(final_mappings)}")
        
        # 解释含义
        print(f"\n含义解释:")
        print(f"1. CSV文件中的列顺序: {','.join(temp_columns)}")
        print(f"2. unique_request字段将从temp_unique_request列读取数据，然后使用bitmap_hash函数转换")
        
    else:
        columns_str = ",".join(columns)
        print(f"\n标准columns参数: {columns_str}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_columns_format()
